import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:firebase_auth/firebase_auth.dart';

import 'create_post.dart';
import 'search_screen.dart';
import 'profile_screen.dart';
import 'wallet_screen.dart';
import '../widgets/home/<USER>';
import 'package:get/get.dart';
import '../controllers/auth_controller.dart';
import '../controllers/profile_controller.dart';
import '../controllers/wallet_controller.dart';
import '../services/post_service.dart';
import '../widgets/profile_drawer.dart';

class MainNavigationScreen extends StatefulWidget {
  const MainNavigationScreen({super.key});

  @override
  State<MainNavigationScreen> createState() => _MainNavigationScreenState();
}

class _MainNavigationScreenState extends State<MainNavigationScreen> {
  int _currentIndex = 0;
  late List<Widget> _screens;

  // Double back to exit functionality
  DateTime? _lastBackPressed;

  // Controllers
  final AuthController _authController = Get.find<AuthController>();
  final ProfileController _profileController = Get.find<ProfileController>();
  final WalletController _walletController = Get.find<WalletController>();

  @override
  void initState() {
    super.initState();
    _initializeServices();
    _initializeScreens();
  }

  void _initializeScreens() {
    final currentUid = _authController.currentUser?.uid ?? '';
    _screens = [
      const HomeContent(), // Home content without bottom nav
      const WalletScreen(), // Re Up tab
      const CreatePostScreen(),
      const SearchScreen(),
      ProfileScreen(userId: currentUid),
    ];
  }

  Future<void> _initializeServices() async {
    try {
      // Wait for AuthController to be fully initialized first
      await _authController.waitForInitialization();

      // Ensure AuthController and ProfileController are ready
      if (_authController.isAuthenticated &&
          _authController.isProfileComplete) {
        // Initialize WalletController if not already initialized
        if (!_walletController.isInitialized) {
          await _walletController.initialize();
        }

        // Initialize PostService
        await PostService().initialize();

        debugPrint(
          'MainNavigationScreen: All services initialized successfully',
        );
      } else {
        debugPrint(
          'MainNavigationScreen: User not authenticated or profile incomplete, skipping service initialization',
        );
      }

      if (mounted) {
        setState(() {
          // Refresh UI after services are ready
        });
      }
    } catch (e) {
      debugPrint('MainNavigationScreen: Error initializing services: $e');
    }
  }

  void _onTabTapped(int index) {
    if (_currentIndex != index) {
      HapticFeedback.selectionClick();
      setState(() {
        _currentIndex = index;
      });
    }
  }

  Future<bool> _onWillPop() async {
    final now = DateTime.now();
    const backPressDuration = Duration(seconds: 2);

    if (_lastBackPressed == null ||
        now.difference(_lastBackPressed!) > backPressDuration) {
      _lastBackPressed = now;

      // Show snackbar with exit message
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Press back again to exit'),
          duration: Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
        ),
      );
      return false; // Don't exit
    }

    return true; // Exit the app
  }

  Widget _buildCompactDrawer() {
    return const ProfileDrawer(isMobileCompact: true);
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) return;
        final shouldExit = await _onWillPop();
        if (shouldExit && mounted) {
          // Exit the app
          SystemNavigator.pop();
        }
      },
      child: LayoutBuilder(
        builder: (context, constraints) {
          final isMobile = constraints.maxWidth < 768;

          return Scaffold(
            body: Center(
              child: Container(
                constraints: BoxConstraints(
                  maxWidth: kIsWeb ? 800 : double.infinity,
                ),
                child: Row(
                  children: [
                    // Permanent Drawer - always visible
                    Container(
                      width: isMobile ? 100 : 230,
                      decoration: BoxDecoration(
                        color: Colors.grey.shade50,
                        border: Border(
                          right: BorderSide(color: Colors.grey.shade200),
                        ),
                      ),
                      child:
                          isMobile
                              ? _buildCompactDrawer()
                              : const ProfileDrawer(),
                    ),
                    // Main Content
                    Expanded(
                      child: Column(
                        children: [
                          // Main screen content
                          Expanded(
                            child: AnimatedSwitcher(
                              duration: const Duration(milliseconds: 250),
                              transitionBuilder: (child, animation) {
                                return SlideTransition(
                                  position: Tween<Offset>(
                                    begin: const Offset(0.05, 0),
                                    end: Offset.zero,
                                  ).animate(
                                    CurvedAnimation(
                                      parent: animation,
                                      curve: Curves.easeInOutCubic,
                                    ),
                                  ),
                                  child: FadeTransition(
                                    opacity: animation,
                                    child: child,
                                  ),
                                );
                              },
                              child: IndexedStack(
                                key: ValueKey(_currentIndex),
                                index: _currentIndex,
                                children: _screens,
                              ),
                            ),
                          ),

                          // Bottom Navigation - only on mobile
                          // if (isMobile)
                          //   Container(
                          //     decoration: BoxDecoration(
                          //       border: Border(
                          //         top: BorderSide(color: Colors.grey.shade200),
                          //       ),
                          //     ),
                          //     child: BottomNavigationBar(
                          //       type: BottomNavigationBarType.fixed,
                          //       selectedItemColor: const Color(0xFF4C5DFF),
                          //       unselectedItemColor: Colors.grey,
                          //       currentIndex: _currentIndex,
                          //       onTap: _onTabTapped,
                          //       elevation: 0,
                          //       backgroundColor: Colors.white,
                          //       items: const [
                          //         BottomNavigationBarItem(
                          //           icon: Icon(Icons.home),
                          //           label: '',
                          //         ),
                          //         BottomNavigationBarItem(
                          //           icon: Icon(Icons.account_balance_wallet),
                          //           label: '',
                          //         ),
                          //         BottomNavigationBarItem(
                          //           icon: Icon(Icons.add_box_outlined),
                          //           label: '',
                          //         ),
                          //         BottomNavigationBarItem(
                          //           icon: Icon(Icons.search),
                          //           label: '',
                          //         ),
                          //         BottomNavigationBarItem(
                          //           icon: Icon(Icons.person_outline),
                          //           label: '',
                          //         ),
                          //       ],
                          //     ),
                          //   ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            bottomNavigationBar: Center(
              heightFactor: kIsWeb ? 1.0 : 0.8,
              child: Container(
                constraints: BoxConstraints(
                  maxWidth: kIsWeb ? 800 : double.infinity,
                ),
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  curve: Curves.easeInOut,
                  child: BottomNavigationBar(
                    type: BottomNavigationBarType.fixed,
                    selectedItemColor: const Color(0xFF4C5DFF),
                    unselectedItemColor: Colors.grey,
                    currentIndex: _currentIndex,
                    onTap: _onTabTapped,
                    elevation: 8,
                    backgroundColor: Colors.white,
                    items: const [
                      BottomNavigationBarItem(
                        icon: Icon(Icons.home),
                        label: '',
                      ),
                      BottomNavigationBarItem(
                        icon: Icon(Icons.account_balance_wallet),
                        label: '',
                      ),
                      BottomNavigationBarItem(
                        icon: Icon(Icons.add_box_outlined),
                        label: '',
                      ),
                      BottomNavigationBarItem(
                        icon: Icon(Icons.search),
                        label: '',
                      ),
                      BottomNavigationBarItem(
                        icon: Icon(Icons.person_outline),
                        label: '',
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
