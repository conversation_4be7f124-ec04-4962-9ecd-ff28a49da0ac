import 'package:get/get.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

/// GetX Controller for managing user profile state across the entire app
class ProfileController extends GetxController {
  static ProfileController get instance => Get.find();

  // Reactive variables for current user profile
  final _currentUserId = ''.obs;
  final _profileImageUrl = ''.obs;
  final _displayName = ''.obs;
  final _username = ''.obs;
  final _bio = ''.obs;
  final _email = ''.obs;
  final _isLoading = false.obs;
  final _postsCount = 0.obs;
  final _followersCount = 0.obs;
  final _followingCount = 0.obs;
  final _walletBalance = 0.0.obs;

  // Cache for other users' profile data
  final _userProfileCache = <String, Map<String, dynamic>>{}.obs;

  // Getters for reactive access
  String get currentUserId => _currentUserId.value;
  String get profileImageUrl => _profileImageUrl.value;
  String get displayName => _displayName.value;
  String get username => _username.value;
  String get bio => _bio.value;
  String get email => _email.value;
  bool get isLoading => _isLoading.value;
  int get postsCount => _postsCount.value;
  int get followersCount => _followersCount.value;
  int get followingCount => _followingCount.value;
  double get walletBalance => _walletBalance.value;

  // Reactive getters for UI binding
  RxString get currentUserIdRx => _currentUserId;
  RxString get profileImageUrlRx => _profileImageUrl;
  RxString get displayNameRx => _displayName;
  RxString get usernameRx => _username;
  RxString get bioRx => _bio;
  RxString get emailRx => _email;
  RxBool get isLoadingRx => _isLoading;
  RxInt get postsCountRx => _postsCount;
  RxInt get followersCountRx => _followersCount;
  RxInt get followingCountRx => _followingCount;
  RxDouble get walletBalanceRx => _walletBalance;

  @override
  void onInit() {
    super.onInit();
    // Don't auto-initialize - let AuthController handle timing
    debugPrint('ProfileController: Initialized (waiting for auth)');
  }

  /// Initialize current user data (called by AuthController)
  Future<void> initializeCurrentUser() async {
    try {
      _isLoading.value = true;

      // First, clear any previous user data to prevent showing old data
      _clearCurrentUserDataOnly();

      final user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        _currentUserId.value = user.uid;
        _email.value = user.email ?? '';

        debugPrint('ProfileController: Loading data for user ${user.uid}');
        await _loadCurrentUserProfile();
        await _loadCurrentUserStats();
      }
    } catch (e) {
      debugPrint('ProfileController: Error initializing current user: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// Load current user's profile data from Firestore
  Future<void> _loadCurrentUserProfile() async {
    try {
      if (_currentUserId.value.isEmpty) {
        debugPrint(
          'ProfileController: Cannot load profile - no current user ID',
        );
        return;
      }

      debugPrint(
        'ProfileController: Loading profile for user ${_currentUserId.value}',
      );

      final userDoc =
          await FirebaseFirestore.instance
              .collection('users')
              .doc(_currentUserId.value)
              .get();

      if (userDoc.exists) {
        final userData = userDoc.data();
        if (userData != null && userData.isNotEmpty) {
          _updateCurrentUserData(userData);

          // Cache the current user's data
          _userProfileCache[_currentUserId.value] = userData;

          debugPrint(
            'ProfileController: Loaded profile for ${_currentUserId.value} - Name: ${userData['name']}, Username: ${userData['username']}',
          );
        } else {
          debugPrint(
            'ProfileController: Profile document exists but is empty for user ${_currentUserId.value}',
          );
        }
      } else {
        debugPrint(
          'ProfileController: No profile document found for user ${_currentUserId.value}',
        );
      }
    } catch (e) {
      debugPrint('ProfileController: Error loading current user profile: $e');
    }
  }

  /// Update current user reactive variables
  void _updateCurrentUserData(Map<String, dynamic> userData) {
    _profileImageUrl.value =
        userData['profileImageUrl'] ?? userData['photoUrl'] ?? '';
    _displayName.value = userData['name'] ?? userData['displayName'] ?? '';
    _username.value = userData['username'] ?? '';
    _bio.value = userData['bio'] ?? '';

    debugPrint(
      'ProfileController: Updated reactive data - Name: ${_displayName.value}, Username: ${_username.value}, Bio: ${_bio.value}',
    );
  }

  /// Load current user's stats (posts, followers, following, wallet)
  Future<void> _loadCurrentUserStats() async {
    try {
      if (_currentUserId.value.isEmpty) return;

      // Load posts count
      final postsQuery =
          await FirebaseFirestore.instance
              .collection('posts')
              .where('authorId', isEqualTo: _currentUserId.value)
              .get();
      _postsCount.value = postsQuery.docs.length;

      // Load followers count
      final followersQuery =
          await FirebaseFirestore.instance
              .collection('follows')
              .where('followingId', isEqualTo: _currentUserId.value)
              .get();
      _followersCount.value = followersQuery.docs.length;

      // Load following count
      final followingQuery =
          await FirebaseFirestore.instance
              .collection('follows')
              .where('followerId', isEqualTo: _currentUserId.value)
              .get();
      _followingCount.value = followingQuery.docs.length;

      debugPrint(
        'ProfileController: Loaded user stats - Posts: ${_postsCount.value}, Followers: ${_followersCount.value}, Following: ${_followingCount.value}',
      );
    } catch (e) {
      debugPrint('ProfileController: Error loading user stats: $e');
    }
  }

  /// Update current user's profile data
  Future<void> updateCurrentUserProfile({
    String? profileImageUrl,
    String? displayName,
    String? username,
    String? bio,
  }) async {
    try {
      if (_currentUserId.value.isEmpty) return;

      _isLoading.value = true;

      final updateData = <String, dynamic>{};

      if (profileImageUrl != null) {
        updateData['profileImageUrl'] = profileImageUrl;
        debugPrint(
          'ProfileController: Updating profile image URL from "${_profileImageUrl.value}" to "$profileImageUrl"',
        );
        _profileImageUrl.value = profileImageUrl;
        debugPrint(
          'ProfileController: Profile image URL updated to "${_profileImageUrl.value}"',
        );
      }

      if (displayName != null) {
        updateData['name'] = displayName;
        updateData['displayName'] = displayName;
        _displayName.value = displayName;
      }

      if (username != null) {
        updateData['username'] = username;
        _username.value = username;
      }

      if (bio != null) {
        updateData['bio'] = bio;
        _bio.value = bio;
      }

      if (updateData.isNotEmpty) {
        updateData['updatedAt'] = FieldValue.serverTimestamp();

        await FirebaseFirestore.instance
            .collection('users')
            .doc(_currentUserId.value)
            .update(updateData);

        // Update cache
        _userProfileCache[_currentUserId.value] = {
          ..._userProfileCache[_currentUserId.value] ?? {},
          ...updateData,
        };

        debugPrint('ProfileController: Updated current user profile');
      }
    } catch (e) {
      debugPrint('ProfileController: Error updating current user profile: $e');
      rethrow;
    } finally {
      _isLoading.value = false;
    }
  }

  /// Get profile data for any user (with caching)
  Future<Map<String, dynamic>?> getUserProfile(String userId) async {
    if (userId.isEmpty) return null;

    // Check cache first
    if (_userProfileCache.containsKey(userId)) {
      return _userProfileCache[userId];
    }

    try {
      final userDoc =
          await FirebaseFirestore.instance
              .collection('users')
              .doc(userId)
              .get();

      if (userDoc.exists) {
        final userData = userDoc.data()!;
        _userProfileCache[userId] = userData;
        return userData;
      }
    } catch (e) {
      debugPrint(
        'ProfileController: Error getting user profile for $userId: $e',
      );
    }

    return null;
  }

  /// Get profile image URL for any user
  Future<String?> getUserProfileImage(String userId) async {
    final userData = await getUserProfile(userId);
    return userData?['profileImageUrl'] ?? userData?['photoUrl'];
  }

  /// Get display name for any user
  Future<String> getUserDisplayName(String userId) async {
    final userData = await getUserProfile(userId);
    return userData?['name'] ??
        userData?['displayName'] ??
        userData?['username'] ??
        'Unknown User';
  }

  /// Get username for any user
  Future<String> getUserUsername(String userId) async {
    final userData = await getUserProfile(userId);
    return userData?['username'] ?? '';
  }

  /// Clear cache for a specific user (useful when user updates profile)
  void clearUserCache(String userId) {
    _userProfileCache.remove(userId);
  }

  /// Clear all cached user data
  void clearAllCache() {
    _userProfileCache.clear();
  }

  /// Clear only current user reactive variables (not cache)
  void _clearCurrentUserDataOnly() {
    _currentUserId.value = '';
    _profileImageUrl.value = '';
    _displayName.value = '';
    _username.value = '';
    _bio.value = '';
    _email.value = '';
    _postsCount.value = 0;
    _followersCount.value = 0;
    _followingCount.value = 0;
    _walletBalance.value = 0.0;
    debugPrint('ProfileController: Current user reactive data cleared');
  }

  /// Clear current user data (called during logout)
  void clearCurrentUserData() {
    _clearCurrentUserDataOnly();
    _isLoading.value = false;
    clearAllCache();
    debugPrint('ProfileController: Current user data and cache cleared');
  }

  /// Refresh current user data
  Future<void> refreshCurrentUser() async {
    await _loadCurrentUserProfile();
    await _loadCurrentUserStats();
  }

  /// Update wallet balance
  void updateWalletBalance(double balance) {
    _walletBalance.value = balance;
  }

  /// Update stats counts
  void updateStats({
    int? postsCount,
    int? followersCount,
    int? followingCount,
  }) {
    if (postsCount != null) _postsCount.value = postsCount;
    if (followersCount != null) _followersCount.value = followersCount;
    if (followingCount != null) _followingCount.value = followingCount;
  }

  /// Check if current user data is available
  bool get hasCurrentUserData =>
      _currentUserId.value.isNotEmpty && _username.value.isNotEmpty;

  /// Get current user's initials for avatar fallback
  String get currentUserInitials {
    if (_displayName.value.isNotEmpty) {
      return _displayName.value
          .split(' ')
          .map((e) => e[0])
          .take(2)
          .join()
          .toUpperCase();
    } else if (_username.value.isNotEmpty) {
      return _username.value.substring(0, 1).toUpperCase();
    }
    return 'U';
  }
}
