import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:money_mouthy_two/controllers/profile_controller.dart';
import 'package:money_mouthy_two/controllers/wallet_controller.dart';

/// GetX Controller for managing authentication state across the entire app
class AuthController extends GetxController {
  static AuthController get instance => Get.find();

  // Firebase instances
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Reactive variables for authentication state
  final _isAuthenticated = false.obs;
  final _isEmailVerified = false.obs;
  final _isProfileComplete = false.obs;
  final _isLoading = false.obs;
  final _errorMessage = ''.obs;
  final _currentUser = Rxn<User>();
  final _isInitialized = false.obs;

  // Profile completion flags
  final _hasUsername = false.obs;
  final _hasDisplayName = false.obs;
  final _profileCompletedFlag = false.obs;

  // Completer for initialization
  Completer<void>? _initializationCompleter;

  // Getters for reactive variables
  bool get isAuthenticated => _isAuthenticated.value;
  bool get isEmailVerified => _isEmailVerified.value;
  bool get isProfileComplete => _isProfileComplete.value;
  bool get isLoading => _isLoading.value;
  String get errorMessage => _errorMessage.value;
  User? get currentUser => _currentUser.value;
  bool get hasUsername => _hasUsername.value;
  bool get hasDisplayName => _hasDisplayName.value;
  bool get profileCompletedFlag => _profileCompletedFlag.value;
  bool get isInitialized => _isInitialized.value;

  // Controllers
  ProfileController get _profileController => Get.find<ProfileController>();
  WalletController get _walletController => Get.find<WalletController>();

  @override
  void onInit() {
    super.onInit();
    _initializeAuthListener();
    debugPrint('AuthController: Initialized');
  }

  /// Wait for authentication state to be fully initialized
  Future<void> waitForInitialization() async {
    if (_isInitialized.value) return;

    if (_initializationCompleter != null) {
      return _initializationCompleter!.future;
    }

    _initializationCompleter = Completer<void>();

    // Wait for auth state to be determined
    final user = _auth.currentUser;
    if (user != null) {
      // User is already logged in, wait for profile data to load
      await _waitForProfileDataLoad();
    } else {
      // No user logged in, mark as initialized
      _markAsInitialized();
    }

    return _initializationCompleter!.future;
  }

  /// Wait for profile data to be loaded for authenticated user
  Future<void> _waitForProfileDataLoad() async {
    int attempts = 0;
    const maxAttempts = 20; // 20 seconds max wait

    while (attempts < maxAttempts) {
      // Check if profile data has been loaded or user is not authenticated
      if (!_isAuthenticated.value ||
          _hasUsername.value ||
          _hasDisplayName.value ||
          _profileCompletedFlag.value) {
        _markAsInitialized();
        return;
      }

      await Future.delayed(const Duration(seconds: 1));
      attempts++;
    }

    // Timeout reached, mark as initialized anyway
    debugPrint(
      'AuthController: Timeout waiting for profile data, marking as initialized',
    );
    _markAsInitialized();
  }

  /// Mark the controller as initialized
  void _markAsInitialized() {
    if (!_isInitialized.value) {
      _isInitialized.value = true;
      _initializationCompleter?.complete();
      debugPrint('AuthController: Marked as initialized');
    }
  }

  @override
  void onClose() {
    super.onClose();
    debugPrint('AuthController: Disposed');
  }

  /// Initialize Firebase auth state listener
  void _initializeAuthListener() {
    _auth.authStateChanges().listen((User? user) async {
      debugPrint('AuthController: Auth state changed - User: ${user?.uid}');

      // Always clear previous user data first to prevent showing wrong data
      _profileController.clearCurrentUserData();

      _currentUser.value = user;

      if (user != null) {
        _isAuthenticated.value = true;
        _isEmailVerified.value = user.emailVerified;

        if (user.emailVerified) {
          await _loadUserProfileStatus();
          await _initializeUserData();
        } else {
          _resetProfileFlags();
        }
      } else {
        _resetAuthState();
      }

      // Mark as initialized after auth state is processed
      _markAsInitialized();
    });
  }

  /// Reset authentication state
  void _resetAuthState() {
    _isAuthenticated.value = false;
    _isEmailVerified.value = false;
    _resetProfileFlags();
    _errorMessage.value = '';

    // Reset wallet controller when user logs out
    try {
      _walletController.reset();
    } catch (e) {
      debugPrint('AuthController: Error resetting wallet controller: $e');
    }

    debugPrint('AuthController: Auth state reset');
  }

  /// Reset profile completion flags
  void _resetProfileFlags() {
    _isProfileComplete.value = false;
    _hasUsername.value = false;
    _hasDisplayName.value = false;
    _profileCompletedFlag.value = false;
  }

  /// Load user profile completion status from Firestore with retry mechanism
  Future<void> _loadUserProfileStatus() async {
    const maxRetries = 3;
    int retryCount = 0;

    while (retryCount < maxRetries) {
      try {
        final user = _currentUser.value;
        if (user == null) return;

        final userDoc = await _firestore
            .collection('users')
            .doc(user.uid)
            .get()
            .timeout(const Duration(seconds: 20));

        if (userDoc.exists) {
          final userData = userDoc.data()!;

          _profileCompletedFlag.value = userData['profileCompleted'] ?? false;
          _hasUsername.value =
              (userData['username']?.toString().isNotEmpty ?? false);
          _hasDisplayName.value =
              (userData['name']?.toString().isNotEmpty ?? false);

          // Enhanced profile completion check
          _isProfileComplete.value =
              _profileCompletedFlag.value ||
              (_hasUsername.value &&
                  _hasDisplayName.value &&
                  _isEmailVerified.value);

          debugPrint(
            'AuthController: Profile status loaded - Complete: ${_isProfileComplete.value}, HasUsername: ${_hasUsername.value}, HasDisplayName: ${_hasDisplayName.value}, EmailVerified: ${_isEmailVerified.value}, ProfileCompletedFlag: ${_profileCompletedFlag.value}',
          );
          return; // Success, exit retry loop
        } else {
          _resetProfileFlags();
          debugPrint('AuthController: User document not found');
          return; // No point retrying if document doesn't exist
        }
      } catch (e) {
        retryCount++;
        debugPrint(
          'AuthController: Error loading profile status (attempt $retryCount): $e',
        );

        if (retryCount >= maxRetries) {
          debugPrint(
            'AuthController: Failed to load profile status after $maxRetries attempts',
          );
          _resetProfileFlags();
          return;
        }

        // Wait before retrying with exponential backoff
        await Future.delayed(Duration(seconds: retryCount * 2));
      }
    }
  }

  /// Initialize user data in ProfileController and WalletController
  Future<void> _initializeUserData() async {
    try {
      debugPrint('AuthController: Initializing user data...');

      // Initialize ProfileController first
      await _profileController.initializeCurrentUser();

      // Initialize WalletController if profile is complete
      if (_isProfileComplete.value) {
        if (!_walletController.isInitialized) {
          await _walletController.initialize();
        }
      }

      debugPrint('AuthController: User data initialization complete');
    } catch (e) {
      debugPrint('AuthController: Error initializing user data: $e');
    }
  }

  /// Sign in with email and password
  Future<bool> signInWithEmailAndPassword(String email, String password) async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      debugPrint('AuthController: Attempting login for $email');

      final credential = await _auth
          .signInWithEmailAndPassword(email: email, password: password)
          .timeout(const Duration(seconds: 30));

      // Reload user to get latest emailVerified flag
      await credential.user?.reload().timeout(const Duration(seconds: 20));
      final user = _auth.currentUser;

      if (user == null) {
        throw FirebaseAuthException(
          code: 'user-not-found',
          message: 'User account not found',
        );
      }

      if (!user.emailVerified) {
        await user.sendEmailVerification().timeout(const Duration(seconds: 20));
        await _auth.signOut();
        _errorMessage.value =
            'Please verify your email first. A new verification link has been sent.';
        return false;
      }

      // Update last login timestamp
      try {
        await _firestore
            .collection('users')
            .doc(user.uid)
            .set({
              'emailVerified': true,
              'lastLogin': FieldValue.serverTimestamp(),
            }, SetOptions(merge: true))
            .timeout(const Duration(seconds: 20));
      } catch (e) {
        debugPrint('AuthController: Unable to update last login: $e');
      }

      debugPrint('AuthController: Login successful for ${user.uid}');
      return true;
    } on FirebaseAuthException catch (e) {
      debugPrint(
        'AuthController: Firebase auth error: ${e.code} - ${e.message}',
      );
      _errorMessage.value = _getAuthErrorMessage(e.code);
      return false;
    } catch (e) {
      debugPrint('AuthController: Login error: $e');
      _errorMessage.value = 'Login failed. Please try again.';
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// Clear error message
  void clearError() {
    _errorMessage.value = '';
  }

  /// Get user-friendly error message from Firebase auth error code
  String _getAuthErrorMessage(String errorCode) {
    switch (errorCode) {
      case 'user-not-found':
        return 'No account found with this email address.';
      case 'wrong-password':
        return 'Incorrect password. Please try again.';
      case 'invalid-email':
        return 'Please enter a valid email address.';
      case 'user-disabled':
        return 'This account has been disabled.';
      case 'too-many-requests':
        return 'Too many failed attempts. Please try again later.';
      case 'network-request-failed':
        return 'Network error. Please check your connection.';
      default:
        return 'Authentication failed. Please try again.';
    }
  }

  /// Sign out user and clear all data
  Future<void> signOut() async {
    try {
      _isLoading.value = true;
      debugPrint('AuthController: Signing out user');

      // Clear all controller data
      _profileController.clearCurrentUserData();

      // Reset wallet controller
      try {
        _walletController.reset();
      } catch (e) {
        debugPrint(
          'AuthController: Error resetting wallet during sign out: $e',
        );
      }

      // Sign out from Firebase
      await _auth.signOut();

      debugPrint('AuthController: Sign out complete');
    } catch (e) {
      debugPrint('AuthController: Error during sign out: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// Create account with email and password
  Future<bool> createAccountWithEmailAndPassword(
    String email,
    String password,
  ) async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      debugPrint('AuthController: Creating account for $email');

      final credential = await _auth
          .createUserWithEmailAndPassword(email: email, password: password)
          .timeout(const Duration(seconds: 30));

      final user = credential.user;
      if (user != null) {
        // Send email verification
        await user.sendEmailVerification().timeout(const Duration(seconds: 20));
        debugPrint('AuthController: Account created, verification email sent');
        return true;
      }

      return false;
    } on FirebaseAuthException catch (e) {
      debugPrint(
        'AuthController: Firebase auth error: ${e.code} - ${e.message}',
      );
      _errorMessage.value = _getAuthErrorMessage(e.code);
      return false;
    } catch (e) {
      debugPrint('AuthController: Account creation error: $e');
      _errorMessage.value = 'Account creation failed. Please try again.';
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// Update profile completion status
  Future<void> updateProfileCompletion({
    bool? hasUsername,
    bool? hasDisplayName,
    bool? profileCompleted,
  }) async {
    if (hasUsername != null) _hasUsername.value = hasUsername;
    if (hasDisplayName != null) _hasDisplayName.value = hasDisplayName;
    if (profileCompleted != null) {
      _profileCompletedFlag.value = profileCompleted;
    }

    // Update overall profile completion status
    _isProfileComplete.value =
        _profileCompletedFlag.value ||
        (_hasUsername.value && _hasDisplayName.value && _isEmailVerified.value);

    debugPrint(
      'AuthController: Profile completion updated - Complete: ${_isProfileComplete.value}',
    );

    // Initialize wallet if profile is now complete
    if (_isProfileComplete.value && !_walletController.isInitialized) {
      try {
        await _walletController.initialize();
      } catch (e) {
        debugPrint(
          'AuthController: Error initializing wallet after profile completion: $e',
        );
      }
    }
  }

  /// Refresh user profile status from Firestore
  Future<void> refreshProfileStatus() async {
    await _loadUserProfileStatus();
  }

  /// Wait for profile status to be loaded and return navigation route
  Future<String> getNavigationRouteAsync() async {
    // Wait for profile status to be loaded if user is authenticated
    if (_isAuthenticated.value && _isEmailVerified.value) {
      // Give some time for the auth state listener to process
      int attempts = 0;
      while (attempts < 10) {
        await Future.delayed(const Duration(milliseconds: 1000));

        // Check if profile status has been loaded
        if (_hasUsername.value ||
            _hasDisplayName.value ||
            _profileCompletedFlag.value) {
          break;
        }

        attempts++;
      }
    }

    return getNavigationRoute();
  }

  /// Get navigation route based on current auth and profile state
  String getNavigationRoute() {
    debugPrint(
      'AuthController: getNavigationRoute - Authenticated: ${_isAuthenticated.value}, EmailVerified: ${_isEmailVerified.value}, ProfileComplete: ${_isProfileComplete.value}, HasUsername: ${_hasUsername.value}, HasDisplayName: ${_hasDisplayName.value}',
    );

    if (!_isAuthenticated.value || !_isEmailVerified.value) {
      return kIsWeb ? '/landing' : '/signup';
    }

    if (_isProfileComplete.value) {
      debugPrint('AuthController: Navigating to /home');
      return '/home';
    } else if (!_hasUsername.value) {
      debugPrint(
        'AuthController: Navigating to /choose_username (no username)',
      );
      return '/choose_username';
    } else {
      debugPrint(
        'AuthController: Navigating to /create_profile (has username, missing other data)',
      );
      return '/create_profile';
    }
  }

  /// Check if user needs to complete onboarding
  bool get needsOnboarding =>
      _isAuthenticated.value &&
      _isEmailVerified.value &&
      !_isProfileComplete.value;

  /// Check if user is ready for main app
  bool get isReadyForMainApp =>
      _isAuthenticated.value &&
      _isEmailVerified.value &&
      _isProfileComplete.value;
}
